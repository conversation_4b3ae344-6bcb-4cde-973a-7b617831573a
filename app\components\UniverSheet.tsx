// app/UniverSheet.tsx
'use client';

import { useEffect, useRef } from 'react'

import { LocaleType, merge, Univer, UniverInstanceType } from "@univerjs/core";
import { FUniver } from "@univerjs/core/facade";
import { defaultTheme } from "@univerjs/design";
 
import { UniverFormulaEnginePlugin } from "@univerjs/engine-formula";
import { UniverRenderEnginePlugin } from "@univerjs/engine-render";
import { UniverUIPlugin } from "@univerjs/ui";
import { UniverDocsPlugin } from "@univerjs/docs";
import { UniverDocsUIPlugin } from "@univerjs/docs-ui";
import { UniverSheetsPlugin } from "@univerjs/sheets";
import { UniverSheetsUIPlugin } from "@univerjs/sheets-ui";
import { UniverSheetsFormulaPlugin } from "@univerjs/sheets-formula";
import { UniverSheetsFormulaUIPlugin } from "@univerjs/sheets-formula-ui";
import { UniverSheetsNumfmtPlugin } from "@univerjs/sheets-numfmt";
import { UniverSheetsNumfmtUIPlugin } from "@univerjs/sheets-numfmt-ui";

 
import DesignZhCN from '@univerjs/design/locale/zh-CN';
import UIZhCN from '@univerjs/ui/locale/zh-CN';
import DocsUIZhCN from '@univerjs/docs-ui/locale/zh-CN';
import SheetsZhCN from '@univerjs/sheets/locale/zh-CN';
import SheetsUIZhCN from '@univerjs/sheets-ui/locale/zh-CN';
import SheetsFormulaUIZhCN from '@univerjs/sheets-formula-ui/locale/zh-CN';
import SheetsNumfmtUIZhCN from '@univerjs/sheets-numfmt-ui/locale/zh-CN';
 
import { UniverSheetsChartPlugin } from '@univerjs-pro/sheets-chart';
import SheetsChartZhCN from '@univerjs-pro/sheets-chart/locale/zh-CN';
import { UniverSheetsChartUIPlugin } from '@univerjs-pro/sheets-chart-ui';
import SheetsChartUIZhCN from '@univerjs-pro/sheets-chart-ui/locale/zh-CN';

import { UniverSheetsPivotTablePlugin } from '@univerjs-pro/sheets-pivot';
import SheetsPivotTableZhCN from '@univerjs-pro/sheets-pivot/locale/zh-CN';
import { UniverSheetsPivotTableUIPlugin } from '@univerjs-pro/sheets-pivot-ui';
import SheetsPivotTableUIZhCN from '@univerjs-pro/sheets-pivot-ui/locale/zh-CN';

import { UniverSheetsConditionalFormattingPlugin } from '@univerjs/sheets-conditional-formatting';
import { UniverSheetsConditionalFormattingUIPlugin } from '@univerjs/sheets-conditional-formatting-ui';
import SheetsConditionalFormattingUIZhCN from '@univerjs/sheets-conditional-formatting-ui/locale/zh-CN';

import { UniverSheetsFilterPlugin } from '@univerjs/sheets-filter';
import { UniverSheetsFilterUIPlugin } from '@univerjs/sheets-filter-ui';
import SheetsFilterUIZhCN from '@univerjs/sheets-filter-ui/locale/zh-CN';

import { UniverSheetsSortPlugin } from '@univerjs/sheets-sort';
import { UniverSheetsSortUIPlugin } from '@univerjs/sheets-sort-ui';
import SheetsSortUIZhCN from '@univerjs/sheets-sort-ui/locale/zh-CN';

import { UniverSheetsTablePlugin } from '@univerjs/sheets-table';
import { UniverSheetsTableUIPlugin } from '@univerjs/sheets-table-ui';
import SheetsTableUIZhCN from '@univerjs/sheets-table-ui/locale/zh-CN';

import { UniverDataValidationPlugin } from '@univerjs/data-validation';
import { UniverSheetsDataValidationPlugin } from '@univerjs/sheets-data-validation';
import { UniverSheetsDataValidationUIPlugin } from '@univerjs/sheets-data-validation-ui';
import SheetsDataValidationUIZhCN from '@univerjs/sheets-data-validation-ui/locale/zh-CN';


import { UniverDocsQuickInsertUIPlugin } from "@univerjs/docs-quick-insert-ui";
import DocsQuickInsertUIZhCN from '@univerjs/docs-quick-insert-ui/locale/zh-CN';

// 这里的 Facade API 是可选的，你可以根据自己的需求来决定是否引入
import '@univerjs/engine-formula/facade';
import '@univerjs/ui/facade';
import '@univerjs/docs-ui/facade';
import '@univerjs/sheets/facade';
import '@univerjs/sheets-ui/facade';
import '@univerjs/sheets-formula/facade';
import '@univerjs/sheets-numfmt/facade';
import '@univerjs-pro/sheets-chart-ui/facade';
import '@univerjs-pro/sheets-pivot/facade';
import '@univerjs/sheets-conditional-formatting/facade';
import '@univerjs/sheets-filter/facade';
import '@univerjs/sheets-sort/facade';
import '@univerjs/sheets-table/facade';
import '@univerjs/sheets-data-validation/facade';
// import '@univerjs/docs-quick-insert-ui/facade';
 
import "@univerjs/design/lib/index.css";
import "@univerjs/ui/lib/index.css";
import "@univerjs/docs-ui/lib/index.css";
import "@univerjs/sheets-ui/lib/index.css";
import "@univerjs/sheets-formula-ui/lib/index.css";
import "@univerjs/sheets-numfmt-ui/lib/index.css";
import '@univerjs-pro/sheets-chart-ui/lib/index.css';
import '@univerjs-pro/sheets-pivot-ui/lib/index.css';
import '@univerjs/sheets-conditional-formatting-ui/lib/index.css';
import '@univerjs/presets/lib/styles/preset-sheets-filter.css';
import '@univerjs/sheets-sort-ui/lib/index.css';
import '@univerjs/sheets-table-ui/lib/index.css';
import '@univerjs/sheets-data-validation-ui/lib/index.css';
import '@univerjs/docs-quick-insert-ui/locale/zh-CN';
import { UniverReadyCallback, UniverInstance, UniverAPI, CellData, WorkbookData } from '@/types/univer';

interface UniverSheetProps {
  onReady?: UniverReadyCallback;
  initialData?: Record<string, unknown>;
}

export default function UniverSheet({ onReady, initialData }: UniverSheetProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const univerAPIRef = useRef<UniverAPI | null>(null);
  const univerInstanceRef = useRef<UniverInstance | null>(null);
  const isMountedRef = useRef<boolean>(true);

  const initialDataRef = useRef(initialData);
  const onReadyRef = useRef(onReady);
  
  // 更新初始数据的函数
  const updateInitialData = (newData: Record<string, unknown>) => {
    if (!isMountedRef.current || !univerAPIRef.current || !newData || Object.keys(newData).length === 0) {
      return;
    }
    
    if (univerAPIRef.current && newData && Object.keys(newData).length > 0) {
      const activeWorkbook = univerAPIRef.current.getActiveWorkbook();
      if (activeWorkbook) {
        const activeWorksheet = activeWorkbook.getActiveSheet();
        if (activeWorksheet) {
          Object.entries(newData).forEach(([cell, value]) => {
            // 解析单元格地址，如 "B1" -> {row: 0, col: 1}
            const match = cell.match(/^([A-Z]+)(\d+)$/);
            if (match) {
              const colStr = match[1];
              const rowNum = parseInt(match[2]) - 1; // 转换为0基索引
              
              // 将列字母转换为数字
              let colNum = 0;
              for (let i = 0; i < colStr.length; i++) {
                colNum = colNum * 26 + (colStr.charCodeAt(i) - 'A'.charCodeAt(0) + 1);
              }
              colNum -= 1; // 转换为0基索引
              
              // 处理数据类型
              let cellValue = value;
              if (typeof value === 'number') {
                cellValue = value;
              } else if (typeof value === 'string' && !isNaN(Number(value)) && value.trim() !== '') {
                cellValue = Number(value);
              }
              
              activeWorksheet.getRange(rowNum, colNum).setValue(cellValue);
            }
          });
          console.log('初始数据已更新到工作表:', newData);
        }
      }
    }
  };

  // 更新onReady回调引用
  useEffect(() => {
    onReadyRef.current = onReady;
  }, [onReady]);

  // 监听initialData变化，但不重新初始化整个组件
  useEffect(() => {
    if (isMountedRef.current && initialData !== initialDataRef.current) {
      initialDataRef.current = initialData;
      updateInitialData(initialData || {});
    }
  }, [initialData]);

  useEffect(() => {
    // 防止重复初始化
    if (univerInstanceRef.current) {
      return;
    }

    isMountedRef.current = true;
    let advancedPluginTimer: NodeJS.Timeout | null = null;

    const initUniver = async () => {
      try {
        const univer = new Univer({
          theme: defaultTheme,
          locale: LocaleType.ZH_CN,
          locales: {
            [LocaleType.ZH_CN]: merge(
              {},
              DesignZhCN,
              UIZhCN,
              DocsUIZhCN,
              SheetsZhCN,
              SheetsUIZhCN,
              SheetsFormulaUIZhCN,
              SheetsNumfmtUIZhCN,
              SheetsChartZhCN,
              SheetsChartUIZhCN,
              SheetsPivotTableZhCN,
              SheetsPivotTableUIZhCN,
              SheetsConditionalFormattingUIZhCN,
              SheetsFilterUIZhCN,
              SheetsSortUIZhCN,
              SheetsTableUIZhCN,
              SheetsDataValidationUIZhCN,
              DocsQuickInsertUIZhCN,
            ),
          },
        });
         
        // 核心插件 - 优先加载
        univer.registerPlugin(UniverRenderEnginePlugin);
        univer.registerPlugin(UniverFormulaEnginePlugin);
         
        univer.registerPlugin(UniverUIPlugin, {
          container: containerRef.current!,
        });
         
        univer.registerPlugin(UniverDocsPlugin);
        univer.registerPlugin(UniverDocsUIPlugin);
         
        univer.registerPlugin(UniverSheetsPlugin);
        univer.registerPlugin(UniverSheetsUIPlugin);
        univer.registerPlugin(UniverSheetsFormulaPlugin);
        univer.registerPlugin(UniverSheetsFormulaUIPlugin);
        univer.registerPlugin(UniverSheetsNumfmtPlugin);
        univer.registerPlugin(UniverSheetsNumfmtUIPlugin);

        // 基础功能插件 - 在Rendered阶段加载
        univer.registerPlugin(UniverSheetsFilterPlugin);
        univer.registerPlugin(UniverSheetsFilterUIPlugin);

        univer.registerPlugin(UniverSheetsSortPlugin);
        univer.registerPlugin(UniverSheetsSortUIPlugin);

        univer.registerPlugin(UniverSheetsConditionalFormattingPlugin);
        univer.registerPlugin(UniverSheetsConditionalFormattingUIPlugin);

        // 添加表格插件作为基础功能
        univer.registerPlugin(UniverSheetsTablePlugin);
        univer.registerPlugin(UniverSheetsTableUIPlugin);

        // 添加数据验证插件
        univer.registerPlugin(UniverDataValidationPlugin, {});
        univer.registerPlugin(UniverSheetsDataValidationPlugin, {});
        univer.registerPlugin(UniverSheetsDataValidationUIPlugin, {});

        // 将透视表插件移到基础功能中
        univer.registerPlugin(UniverSheetsPivotTablePlugin);
        univer.registerPlugin(UniverSheetsPivotTableUIPlugin);

        console.log('基础功能插件加载完成');

        // 高级功能插件将在Steady生命周期延迟加载
        const loadAdvancedPlugins = () => {
          try {
            // 检查组件是否仍然挂载且Univer实例仍然有效
            if (!isMountedRef.current || !univerInstanceRef.current) {
              console.log('组件已卸载或Univer实例已销毁，跳过高级插件加载');
              return;
            }

            const currentUniver = univerInstanceRef.current;
            
            currentUniver.registerPlugin(UniverSheetsChartPlugin);
            currentUniver.registerPlugin(UniverSheetsChartUIPlugin);

            currentUniver.registerPlugin(UniverDocsQuickInsertUIPlugin);
            
            console.log('高级功能插件延迟加载完成');
          } catch (error) {
            console.warn('高级功能插件延迟加载失败:', error);
          }
        };

        // 监听Steady生命周期，延迟3秒后加载高级功能插件
        advancedPluginTimer = setTimeout(() => {
          loadAdvancedPlugins();
        }, 3000);
        
        // 准备工作表数据，包含初始数据
        const workbookData: WorkbookData = {
          id: 'workbook-01',
          locale: LocaleType.ZH_CN,
          name: 'UniverSheet',
          sheetOrder: ['sheet-01'],
          sheets: {
            'sheet-01': {
              id: 'sheet-01',
              name: '工作表1',
              cellData: {},
            },
          },
        };

        // 如果有初始数据，直接添加到工作表数据中
        if (initialDataRef.current && Object.keys(initialDataRef.current).length > 0) {
          Object.entries(initialDataRef.current).forEach(([cell, value]) => {
            // 解析单元格地址，如 "B1" -> {row: 0, col: 1}
            const match = cell.match(/^([A-Z]+)(\d+)$/);
            if (match) {
              const colStr = match[1];
              const rowNum = parseInt(match[2]) - 1; // 转换为0基索引
              
              // 将列字母转换为数字
              let colNum = 0;
              for (let i = 0; i < colStr.length; i++) {
                colNum = colNum * 26 + (colStr.charCodeAt(i) - 'A'.charCodeAt(0) + 1);
              }
              colNum -= 1; // 转换为0基索引
              
              if (!workbookData.sheets['sheet-01'].cellData[rowNum]) {
                workbookData.sheets['sheet-01'].cellData[rowNum] = {};
              }
              
              // 处理数据类型，确保数字被正确识别和格式化
              let cellValue = value;
              let cellType: number | undefined;
              
              // 如果是数字类型，确保正确设置
              if (typeof value === 'number') {
                cellValue = value;
                cellType = 2; // 数字类型
              }
              // 如果是字符串且看起来像数字，转换为数字
              else if (typeof value === 'string' && !isNaN(Number(value)) && value.trim() !== '') {
                cellValue = Number(value);
                cellType = 2; // 数字类型
              }
              
              const cellData: CellData = {
                v: cellValue,
              };
              
              // 如果是数字类型，设置类型标识
              if (cellType !== undefined) {
                cellData.t = cellType;
              }
              
              workbookData.sheets['sheet-01'].cellData[rowNum][colNum] = cellData;
            }
          });
          console.log('初始数据已集成到工作表:', initialDataRef.current);
        }

        univer.createUnit(UniverInstanceType.UNIVER_SHEET, workbookData);
   
        const univerAPI = FUniver.newAPI(univer);
        univerAPIRef.current = univerAPI;
        univerInstanceRef.current = univer;
        
        console.log('Univer实例已准备就绪');
        
        // 将API暴露到全局变量
        (window as unknown as Record<string, unknown>).univerAPI = univerAPI;
        (window as unknown as Record<string, unknown>).univerInstance = univer;
        
        // 通知父组件Univer实例已准备就绪
        if (onReadyRef.current) {
          onReadyRef.current(univer, univerAPI);
        }
      } catch (error) {
        console.error('Univer初始化失败:', error);
      }
    };

    initUniver();

    return () => {
      // 标记组件已卸载
      isMountedRef.current = false;
      
      // 清理定时器
      if (advancedPluginTimer) {
        clearTimeout(advancedPluginTimer);
      }
      
      // 清理资源
      try {
        if (univerAPIRef.current) {
          univerAPIRef.current.dispose?.();
          univerAPIRef.current = null;
        }
        if (univerInstanceRef.current) {
          univerInstanceRef.current.dispose?.();
          univerInstanceRef.current = null;
        }
      } catch (error) {
        // 只在组件仍然挂载时记录错误
        if (isMountedRef.current) {
          console.error('Univer清理失败:', error);
        }
      }
    };
  }, []); // 移除所有依赖，防止重复初始化

  return (
    <div 
      ref={containerRef} 
      className="univer-container"
      // style={{ height: '80vh', width: '50%' }}
    />
  );
}