import { test, expect } from '@playwright/test';

test.describe('数据验证功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 直接导航到仪表板（假设已经有会话）
    await page.goto('http://localhost:3000/dashboard');
    await page.waitForTimeout(5000); // 等待页面加载
  });

  test('简单数据验证任务 - 数值范围验证', async ({ page }) => {
    // 进入数据验证关卡
    await page.locator('div').filter({ hasText: /^数据验证学习使用数据验证确保数据输入的准确性难度:★★★★★积分:300子任务数:2待解锁$/ }).getByRole('link').click();
    await page.waitForTimeout(3000);

    // 进入简单数据验证任务
    await page.locator('div').filter({ hasText: /^简单数据验证学习基础数据验证设置难度:★★★★★积分:150任务数:1得分:-进度0%开始挑战$/ }).getByRole('link').click();
    await page.waitForTimeout(8000); // 等待Univer加载

    // 验证页面元素
    await expect(page.getByRole('heading', { name: '数值范围验证' })).toBeVisible();
    await expect(page.getByText('任务类型: dataValidation')).toBeVisible();
    await expect(page.getByText('为B2:B6单元格设置数据验证，只允许输入1-100之间的整数')).toBeVisible();

    // 验证Excel练习区已加载
    await expect(page.getByRole('heading', { name: 'Excel练习区' })).toBeVisible();
    await expect(page.locator('canvas')).toBeVisible(); // Univer canvas

    // 测试未设置数据验证时的验证结果
    await page.getByRole('button', { name: '提交任务' }).click();
    await expect(page.getByText('任务未完成，请检查你的操作是否正确')).toBeVisible();

    // 检查控制台是否有错误
    const logs = await page.evaluate(() => {
      return window.console.logs || [];
    });
    
    console.log('简单数据验证任务测试完成');
  });

  test('复杂数据验证任务 - 下拉列表验证', async ({ page }) => {
    // 进入数据验证关卡
    await page.locator('div').filter({ hasText: /^数据验证学习使用数据验证确保数据输入的准确性难度:★★★★★积分:300子任务数:2待解锁$/ }).getByRole('link').click();
    await page.waitForTimeout(3000);

    // 进入复杂数据验证任务
    await page.locator('div').filter({ hasText: /^复杂数据验证学习高级数据验证设置难度:★★★★★积分:150任务数:1得分:-进度0%开始挑战$/ }).getByRole('link').click();
    await page.waitForTimeout(8000); // 等待Univer加载

    // 验证页面元素
    await expect(page.getByRole('heading', { name: '下拉列表验证' })).toBeVisible();
    await expect(page.getByText('任务类型: dataValidation')).toBeVisible();
    await expect(page.getByText('为C2:C6单元格设置下拉列表验证，选项为：优秀、良好、一般、较差')).toBeVisible();

    // 验证Excel练习区已加载
    await expect(page.getByRole('heading', { name: 'Excel练习区' })).toBeVisible();
    await expect(page.locator('canvas')).toBeVisible(); // Univer canvas

    // 测试未设置数据验证时的验证结果
    await page.getByRole('button', { name: '提交任务' }).click();
    await expect(page.getByText('任务未完成，请检查你的操作是否正确')).toBeVisible();

    console.log('复杂数据验证任务测试完成');
  });

  test('数据验证插件加载测试', async ({ page }) => {
    // 进入任意数据验证任务
    await page.locator('div').filter({ hasText: /^数据验证学习使用数据验证确保数据输入的准确性难度:★★★★★积分:300子任务数:2待解锁$/ }).getByRole('link').click();
    await page.waitForTimeout(3000);
    
    await page.locator('div').filter({ hasText: /^简单数据验证学习基础数据验证设置难度:★★★★★积分:150任务数:1得分:-进度0%开始挑战$/ }).getByRole('link').click();
    await page.waitForTimeout(8000);

    // 检查控制台日志，确认数据验证插件已加载
    const consoleLogs = [];
    page.on('console', msg => {
      consoleLogs.push(msg.text());
    });

    // 等待一段时间收集日志
    await page.waitForTimeout(2000);

    // 验证关键日志
    const hasUniverReady = consoleLogs.some(log => log.includes('Univer实例已准备就绪'));
    const hasPluginsLoaded = consoleLogs.some(log => log.includes('基础功能插件加载完成') || log.includes('高级功能插件延迟加载完成'));
    
    expect(hasUniverReady).toBeTruthy();
    expect(hasPluginsLoaded).toBeTruthy();

    console.log('数据验证插件加载测试完成');
  });

  test('数据验证API测试', async ({ page }) => {
    // 进入简单数据验证任务
    await page.locator('div').filter({ hasText: /^数据验证学习使用数据验证确保数据输入的准确性难度:★★★★★积分:300子任务数:2待解锁$/ }).getByRole('link').click();
    await page.waitForTimeout(3000);
    
    await page.locator('div').filter({ hasText: /^简单数据验证学习基础数据验证设置难度:★★★★★积分:150任务数:1得分:-进度0%开始挑战$/ }).getByRole('link').click();
    await page.waitForTimeout(8000);

    // 测试数据验证API是否可用
    const apiTest = await page.evaluate(() => {
      try {
        // 检查全局Univer实例
        if (typeof window.univerInstance === 'undefined') {
          return { success: false, error: 'Univer实例未找到' };
        }

        const workbook = window.univerInstance.getActiveWorkbook();
        if (!workbook) {
          return { success: false, error: '无法获取工作簿' };
        }

        const worksheet = workbook.getActiveSheet();
        if (!worksheet) {
          return { success: false, error: '无法获取工作表' };
        }

        // 测试获取范围
        const range = worksheet.getRange('B2:B6');
        if (!range) {
          return { success: false, error: '无法获取范围B2:B6' };
        }

        // 测试数据验证API（即使没有设置验证规则）
        try {
          const dataValidations = range.getDataValidations();
          return { 
            success: true, 
            hasDataValidations: Array.isArray(dataValidations),
            dataValidationCount: dataValidations ? dataValidations.length : 0
          };
        } catch (e) {
          return { success: false, error: `数据验证API错误: ${e.message}` };
        }
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    console.log('数据验证API测试结果:', apiTest);
    expect(apiTest.success).toBeTruthy();

    console.log('数据验证API测试完成');
  });
});
