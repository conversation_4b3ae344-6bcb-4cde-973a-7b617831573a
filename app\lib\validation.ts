/**
 * Excel验证服务 - 基于官方Univer FFilter API
 */

export interface ValidationRule {
  type: 'filter' | 'sort' | 'format' | 'formula' | 'conditional_format' | 'multiSort' | 'conditionalFormat' | 'multiConditionalFormat' | 'dataValidation' | 'simpleDataValidation' | 'complexDataValidation'
  dataRange?: string
  expectedVisibleRows?: number
  expectedFilteredData?: Array<Record<string, unknown>>
  expectedOrder?: unknown[]
  sortColumn?: string
  sortDirection?: 'asc' | 'desc'
  // 排序相关字段
  column?: string
  direction?: 'asc' | 'desc'
  sorts?: Array<{ column: string; direction: 'asc' | 'desc' }>
  // 条件格式相关字段
  range?: string
  condition?: string
  value?: any
  expectedFormattedCells?: string[]
  conditions?: Array<{ type: string; value?: any; minValue?: any; maxValue?: any; color: string }>
  expectedResults?: Record<string, string[]>
  // 其他字段
  conditionRange?: string
  expectedBackgroundColor?: string
  // 数据验证相关字段
  validationType?: 'number' | 'integer' | 'textLength' | 'date' | 'checkbox' | 'list' | 'custom'
  validationOperator?: 'between' | 'notBetween' | 'equalTo' | 'notEqualTo' | 'greaterThan' | 'greaterThanOrEqualTo' | 'lessThan' | 'lessThanOrEqualTo'
  validationValues?: any[]
  listValues?: string[]
  allowBlank?: boolean
  showInputMessage?: boolean
  inputTitle?: string
  inputMessage?: string
  showErrorMessage?: boolean
  errorTitle?: string
  errorMessage?: string
  errorStyle?: 'stop' | 'warning' | 'information'
  expectedValidCells?: string[]
  expectedInvalidCells?: string[]
  expectedTextColor?: string
  formulaCell?: string
  expectedFormula?: string
  expectedResult?: unknown
}

export interface ValidationResult {
  success: boolean
  message: string
  details?: Record<string, unknown>
}

export interface UniverAPI {
  getActiveWorkbook(): any
}

export class ExcelValidationService {
  constructor(private univerAPI: UniverAPI) {}

  /**
   * 验证任务
   */
  async validateTask(rule: ValidationRule): Promise<ValidationResult> {
    switch (rule.type) {
      case 'filter':
        return await this.validateFilter(rule)
      case 'sort':
        return await this.validateSort(rule)
      case 'multiSort':
        return await this.validateMultiSort(rule)
      case 'format':
        return await this.validateFormat(rule)
      case 'formula':
        return await this.validateFormula(rule)
      case 'conditional_format':
      case 'conditionalFormat':
      case 'multiConditionalFormat':
        return await this.validateConditionalFormat(rule)
      case 'dataValidation':
      case 'simpleDataValidation':
      case 'complexDataValidation':
        return await this.validateDataValidation(rule)
      default:
        return {
          success: false,
          message: `不支持的验证类型: ${rule.type}`
        }
    }
  }

  /**
   * 验证筛选功能 - 基于官方FFilter API
   */
  private async validateFilter(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.dataRange) {
      return {
        success: false,
        message: '验证规则配置错误：缺少数据范围'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      const worksheet = workbook.getActiveSheet()

      // 使用官方FFilter API进行验证
      const filter = worksheet.getFilter()

      if (!filter) {
        return {
          success: false,
          message: '未检测到筛选器。请确保已正确执行以下步骤：\n\n1. 选择数据范围（包含表头）\n2. 启用筛选功能（数据 → 筛选）\n3. 设置筛选条件\n\n提示：筛选功能会在表头显示下拉箭头。',
          details: {
            filterDetected: false,
            hint: '请先启用筛选功能'
          }
        }
      }

      // 获取筛选范围
      const filterRange = filter.getRange()

      // 获取被筛选掉的行
      const filteredOutRows = filter.getFilteredOutRows()

      // 检查是否有筛选条件被设置
      let hasFilterCriteria = false
      const filterCriteriaDetails: any = {}

      if (filterRange) {
        const rangeNotation = filterRange.getA1Notation()
        const rangeMatch = rangeNotation.match(/([A-Z]+)(\d+):([A-Z]+)(\d+)/)

        if (rangeMatch) {
          const startCol = rangeMatch[1]
          const endCol = rangeMatch[3]
          const startColIndex = this.getColumnIndex(startCol)
          const endColIndex = this.getColumnIndex(endCol)

          for (let colIndex = startColIndex; colIndex <= endColIndex; colIndex++) {
            try {
              const criteria = filter.getColumnFilterCriteria(colIndex)
              if (criteria) {
                hasFilterCriteria = true
                filterCriteriaDetails[`column_${colIndex}`] = criteria
              }
            } catch (error) {
            }
          }
        }
      }

      // 验证筛选是否真正应用
      if (!hasFilterCriteria) {
        return {
          success: false,
          message: '未检测到筛选条件。请确保已正确设置筛选条件：\n\n1. 点击表头的下拉箭头\n2. 取消选中"全选"\n3. 只勾选需要显示的值\n4. 点击"确定"\n\n提示：设置筛选条件后，部分行会被隐藏。',
          details: {
            filterDetected: true,
            hasFilterCriteria: false,
            filterRange: filterRange?.getA1Notation(),
            hint: '筛选器已启用但未设置筛选条件'
          }
        }
      }

      // 验证筛选条件是否符合预期
      if (rule.expectedFilteredData && rule.expectedFilteredData.length > 0) {
        const criteriaValidation = await this.validateFilterCriteria(filterCriteriaDetails, rule)
        if (!criteriaValidation.isValid) {
          return {
            success: false,
            message: criteriaValidation.message || '筛选条件不符合预期',
            details: {
              filterDetected: true,
              hasFilterCriteria: true,
              filterCriteria: filterCriteriaDetails,
              criteriaValidation: criteriaValidation,
              hint: '筛选条件设置不正确'
            }
          }
        }
      }

      // 验证筛选结果
      if (filteredOutRows && filteredOutRows.length > 0) {

        // 验证被筛选掉的行数是否合理
        if (rule.expectedVisibleRows !== undefined) {
          const range = worksheet.getRange(rule.dataRange)
          const values = range.getValues()
          const totalRows = values.length - 1 // 减去表头
          const expectedFilteredOutRows = totalRows - rule.expectedVisibleRows

          if (filteredOutRows.length !== expectedFilteredOutRows) {
            return {
              success: false,
              message: `筛选结果不正确。期望显示 ${rule.expectedVisibleRows} 行数据，但实际筛选掉了 ${filteredOutRows.length} 行（应该筛选掉 ${expectedFilteredOutRows} 行）。\n\n请检查筛选条件设置。`,
              details: {
                filterDetected: true,
                hasFilterCriteria: true,
                totalRows: totalRows,
                expectedVisibleRows: rule.expectedVisibleRows,
                expectedFilteredOutRows: expectedFilteredOutRows,
                actualFilteredOutRows: filteredOutRows.length,
                filteredOutRows: filteredOutRows,
                hint: '筛选结果行数不正确'
              }
            }
          }
        }
      } else {
        // 没有行被筛选掉，可能筛选条件包含了所有数据

        if (rule.expectedVisibleRows !== undefined) {
          const range = worksheet.getRange(rule.dataRange)
          const values = range.getValues()
          const totalRows = values.length - 1 // 减去表头

          if (totalRows !== rule.expectedVisibleRows) {
            return {
              success: false,
              message: `筛选条件可能设置不正确。期望显示 ${rule.expectedVisibleRows} 行数据，但当前显示 ${totalRows} 行。\n\n请检查筛选条件是否正确设置。`,
              details: {
                filterDetected: true,
                hasFilterCriteria: true,
                totalRows: totalRows,
                expectedVisibleRows: rule.expectedVisibleRows,
                filteredOutRows: [],
                hint: '筛选条件可能包含了所有数据'
              }
            }
          }
        }
      }

      return {
        success: true,
        message: '筛选验证通过！筛选功能使用正确。',
        details: {
          filterDetected: true,
          hasFilterCriteria: true,
          filterRange: filterRange?.getA1Notation(),
          filterCriteria: filterCriteriaDetails,
          filteredOutRows: filteredOutRows || [],
          hint: '筛选功能使用正确'
        }
      }

    } catch (error) {
      return {
        success: false,
        message: `验证筛选时发生错误: ${error}`,
        details: { error: String(error) }
      }
    }
  }

  /**
   * 验证筛选条件是否符合预期
   */
  private async validateFilterCriteria(filterCriteria: any, rule: ValidationRule): Promise<{ isValid: boolean; message?: string }> {
    try {
      if (!rule.expectedFilteredData || rule.expectedFilteredData.length === 0) {
        return { isValid: true }
      }

      // 分析期望数据的筛选模式
      const expectedPatterns: any = {}

      for (const row of rule.expectedFilteredData) {
        for (const [colKey, value] of Object.entries(row)) {
          const colIndex = parseInt(colKey)
          if (!expectedPatterns[colIndex]) {
            expectedPatterns[colIndex] = new Set()
          }
          expectedPatterns[colIndex].add(value)
        }
      }

      // 检查筛选条件是否与期望模式匹配
      for (const [colKey, criteria] of Object.entries(filterCriteria)) {
        const colIndex = parseInt(colKey.split('_')[1])
        const expectedValues = expectedPatterns[colIndex]

        if (expectedValues && criteria && (criteria as any).filters && (criteria as any).filters.filters) {
          const actualFilters = new Set((criteria as any).filters.filters)
          const expectedFiltersArray = Array.from(expectedValues)

          // 检查筛选条件是否包含期望的值
          let hasExpectedValues = false
          for (const expectedValue of expectedFiltersArray) {
            if (actualFilters.has(String(expectedValue))) {
              hasExpectedValues = true
              break
            }
          }

          if (!hasExpectedValues) {
            return {
              isValid: false,
              message: `列${colIndex + 1}的筛选条件不正确。期望包含值: ${expectedFiltersArray.join(', ')}，实际筛选条件: ${Array.from(actualFilters).join(', ')}`
            }
          }
        }
      }

      return { isValid: true }
    } catch (error) {
      return { isValid: true } // 验证失败时默认通过，避免误判
    }
  }

  /**
   * 获取列索引（A=0, B=1, C=2...）
   */
  private getColumnIndex(column: string): number {
    let result = 0
    for (let i = 0; i < column.length; i++) {
      result = result * 26 + (column.charCodeAt(i) - 'A'.charCodeAt(0) + 1)
    }
    return result - 1
  }

  /**
   * 验证单列排序
   */
  private async validateSort(rule: ValidationRule): Promise<ValidationResult> {
    try {

      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return { success: false, message: '无法获取工作簿' }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return { success: false, message: '无法获取工作表' }
      }

      // 确定数据范围，如果没有指定则使用默认范围
      const dataRange = rule.dataRange || 'A1:C6'
      const range = worksheet.getRange(dataRange)
      const values = range.getValues()

      if (!values || values.length < 2) {
        return { success: false, message: '数据范围内没有足够的数据进行排序验证' }
      }

      // 获取排序列索引
      const sortColumn = rule.column || rule.sortColumn
      if (!sortColumn) {
        return { success: false, message: '未指定排序列' }
      }

      const columnIndex = this.getColumnIndex(sortColumn)
      const direction = rule.direction || rule.sortDirection || 'asc'

      // 跳过表头，从第二行开始验证
      const dataRows = values.slice(1)

      // 验证数据是否按指定顺序排序
      const isCorrectlySorted = this.checkSortOrder(dataRows, columnIndex, direction)

      if (!isCorrectlySorted) {
        return {
          success: false,
          message: `数据未按${sortColumn}列${direction === 'asc' ? '升序' : '降序'}排序`
        }
      }

      // 如果有期望的顺序，验证是否匹配
      if (rule.expectedOrder && rule.expectedOrder.length > 0) {
        const actualOrder = dataRows.map((row: any) => row[0]) // 假设第一列是标识列
        const isOrderCorrect = this.checkExpectedOrder(actualOrder, rule.expectedOrder)

        if (!isOrderCorrect) {
          return {
            success: false,
            message: `排序结果与期望顺序不匹配。期望: ${rule.expectedOrder.join(', ')}，实际: ${actualOrder.join(', ')}`
          }
        }
      }

      return { success: true, message: '排序验证通过' }
    } catch (error) {
      return { success: false, message: `排序验证过程中发生错误: ${error}` }
    }
  }

  /**
   * 验证多列排序
   */
  private async validateMultiSort(rule: ValidationRule): Promise<ValidationResult> {
    try {

      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return { success: false, message: '无法获取工作簿' }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return { success: false, message: '无法获取工作表' }
      }

      // 确定数据范围
      const dataRange = rule.dataRange || 'A1:C7'
      const range = worksheet.getRange(dataRange)
      const values = range.getValues()

      if (!values || values.length < 2) {
        return { success: false, message: '数据范围内没有足够的数据进行排序验证' }
      }

      if (!rule.sorts || rule.sorts.length === 0) {
        return { success: false, message: '未指定排序条件' }
      }

      // 跳过表头，从第二行开始验证
      const dataRows = values.slice(1)

      // 验证多列排序
      const isCorrectlySorted = this.checkMultiColumnSortOrder(dataRows, rule.sorts)

      if (!isCorrectlySorted) {
        const sortDesc = rule.sorts.map(s => `${s.column}列${s.direction === 'asc' ? '升序' : '降序'}`).join('，')
        return {
          success: false,
          message: `数据未按指定条件排序: ${sortDesc}`
        }
      }

      // 如果有期望的顺序，验证是否匹配
      if (rule.expectedOrder && rule.expectedOrder.length > 0) {
        const actualOrder = dataRows.map((row: any) => row[0]) // 假设第一列是标识列
        const isOrderCorrect = this.checkExpectedOrder(actualOrder, rule.expectedOrder)

        if (!isOrderCorrect) {
          return {
            success: false,
            message: `排序结果与期望顺序不匹配。期望: ${rule.expectedOrder.join(', ')}，实际: ${actualOrder.join(', ')}`
          }
        }
      }

      return { success: true, message: '多列排序验证通过' }
    } catch (error) {
      return { success: false, message: `多列排序验证过程中发生错误: ${error}` }
    }
  }

  /**
   * 检查单列排序顺序
   */
  private checkSortOrder(dataRows: any[], columnIndex: number, direction: string): boolean {
    for (let i = 0; i < dataRows.length - 1; i++) {
      const current = dataRows[i][columnIndex]
      const next = dataRows[i + 1][columnIndex]

      if (direction === 'asc') {
        if (current > next) return false
      } else {
        if (current < next) return false
      }
    }
    return true
  }

  /**
   * 检查多列排序顺序
   */
  private checkMultiColumnSortOrder(dataRows: any[], sorts: Array<{ column: string; direction: 'asc' | 'desc' }>): boolean {
    for (let i = 0; i < dataRows.length - 1; i++) {
      const current = dataRows[i]
      const next = dataRows[i + 1]

      for (const sort of sorts) {
        const columnIndex = this.getColumnIndex(sort.column)
        const currentValue = current[columnIndex]
        const nextValue = next[columnIndex]

        if (currentValue !== nextValue) {
          if (sort.direction === 'asc') {
            if (currentValue > nextValue) return false
          } else {
            if (currentValue < nextValue) return false
          }
          break // 如果当前排序条件已经决定了顺序，就不需要检查后续条件
        }
      }
    }
    return true
  }

  /**
   * 检查期望顺序
   */
  private checkExpectedOrder(actualOrder: unknown[], expectedOrder: unknown[]): boolean {
    if (actualOrder.length !== expectedOrder.length) {
      return false
    }

    for (let i = 0; i < actualOrder.length; i++) {
      if (actualOrder[i] !== expectedOrder[i]) {
        return false
      }
    }

    return true
  }

  private async validateFormat(_rule: ValidationRule): Promise<ValidationResult> {
    return { success: true, message: '格式验证暂未实现' }
  }

  private async validateFormula(_rule: ValidationRule): Promise<ValidationResult> {
    return { success: true, message: '公式验证暂未实现' }
  }

  private async validateConditionalFormat(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      const worksheet = workbook.getActiveSheet()

      // 暂时简化验证逻辑，避免API调用错误
      // 实际应用中需要根据Univer的具体API文档来实现

      // 验证简单条件格式
      if (rule.type === 'conditionalFormat') {
        return await this.validateSimpleConditionalFormat(rule, worksheet, [])
      }

      // 验证多条件格式
      if (rule.type === 'multiConditionalFormat') {
        return await this.validateMultiConditionalFormat(rule, worksheet, [])
      }

      return {
        success: false,
        message: `不支持的条件格式验证类型: ${rule.type}`
      }
    } catch (error) {
      return {
        success: false,
        message: '条件格式验证过程中发生错误，请重试'
      }
    }
  }

  /**
   * 验证简单条件格式
   */
  private async validateSimpleConditionalFormat(rule: any, worksheet: any, conditionalFormattingRules: any[]): Promise<ValidationResult> {
    const { range, condition, value, expectedFormattedCells, expectedBackgroundColor } = rule

    try {
      // 使用Univer API获取用户设置的条件格式规则
      const fWorksheet = worksheet

      // 获取指定范围的条件格式规则（只检查，不修改）
      const fRange = fWorksheet.getRange(range)
      const conditionalRules = fRange.getConditionalFormattingRules()

      // 如果没有检测到条件格式规则，说明用户还没有设置条件格式
      if (!conditionalRules || conditionalRules.length === 0) {

        // 不自动设置，只提示用户操作
        return {
          success: false,
          message: `请设置条件格式。\n\n操作步骤：\n1. 选择数据范围 ${range}\n2. 点击"数据"菜单中的"条件格式"\n3. 选择"突出显示单元格规则" → "大于"\n4. 输入条件值：${value}\n5. 选择红色背景色（在颜色选择器中选择第3行第3列的红色）\n6. 点击"确定"\n\n注意：验证的是背景色（#FF0000），不是文字色！`
        }
      }

      // 如果能获取到条件格式规则，进行规则验证
      if (conditionalRules && conditionalRules.length > 0) {

        // 验证条件格式规则是否正确
        const ruleValidation = this.validateConditionalFormattingRule(conditionalRules, {
          range,
          condition,
          value,
          expectedBackgroundColor
        })

        if (!ruleValidation.isValid) {
          return {
            success: false,
            message: `条件格式规则设置不正确：${ruleValidation.message}\n\n请确保：\n1. 条件类型为"大于"\n2. 条件值为 ${value}\n3. 背景色为红色（支持多种格式：${expectedBackgroundColor}、rgb(245,82,82)、#f05252等）\n4. 应用范围为 ${range}\n\n注意：验证的是背景色，不是文字色！颜色比较不区分大小写。`
          }
        }

        // 如果规则验证通过，返回成功
        return {
          success: true,
          message: '条件格式验证成功！已正确设置条件格式规则。'
        }
      }

      // 无论是否能获取到规则，都要验证单元格的实际格式效果

      if (expectedFormattedCells && expectedFormattedCells.length > 0) {
        const formattedCellsFound = []
        const unformattedCells = []

        for (const cellAddress of expectedFormattedCells) {
          const hasCorrectFormat = await this.checkCellBackgroundColor(worksheet, cellAddress, expectedBackgroundColor)

          if (hasCorrectFormat) {
            formattedCellsFound.push(cellAddress)
          } else {
            unformattedCells.push(cellAddress)
          }
        }

        // 检查是否所有期望的单元格都有正确的格式
        if (unformattedCells.length > 0) {
          return {
            success: false,
            message: `条件格式应用不完整。以下单元格缺少红色背景格式：${unformattedCells.join(', ')}\n\n请确保：\n1. 选择数据范围 ${range}\n2. 设置条件：大于 ${value}\n3. 选择红色背景色（在颜色选择器中选择第3行第3列的红色）\n4. 点击"确定"应用格式\n\n注意：验证的是背景色（#FF0000），不是文字色！`
          }
        }

        // 检查是否有不应该格式化的单元格被格式化了
        const allCellsInRange = this.getCellsInRange(range)
        const unexpectedFormattedCells = []

        for (const cellAddress of allCellsInRange) {
          if (!expectedFormattedCells.includes(cellAddress)) {
            const hasFormat = await this.checkCellBackgroundColor(worksheet, cellAddress, expectedBackgroundColor)
            if (hasFormat) {
              unexpectedFormattedCells.push(cellAddress)
            }
          }
        }

        if (unexpectedFormattedCells.length > 0) {
          return {
            success: false,
            message: `条件格式设置错误。以下单元格不应该有红色背景格式：${unexpectedFormattedCells.join(', ')}\n\n请检查条件值是否设置正确（应该是 ${value}）。`
          }
        }

        return {
          success: true,
          message: '条件格式验证成功！已正确设置条件格式规则。'
        }
      }

      return {
        success: false,
        message: `请设置条件格式。\n\n操作步骤：\n1. 选择数据范围 ${range}\n2. 点击"数据"菜单中的"条件格式"\n3. 选择"突出显示单元格规则" → "大于"\n4. 输入条件值：${value}\n5. 选择红色背景色（在颜色选择器中选择第3行第3列的红色）\n6. 点击"确定"\n\n注意：验证的是背景色（#FF0000），不是文字色！`
      }
    } catch (error) {
      return {
        success: false,
        message: '验证过程中发生错误，请重试'
      }
    }
  }

  /**
   * 验证多条件格式
   */
  private async validateMultiConditionalFormat(rule: any, worksheet: any, conditionalFormattingRules: any[]): Promise<ValidationResult> {
    const { range, conditions, expectedResults } = rule

    try {
      // 使用Univer API获取用户设置的条件格式规则
      const fWorksheet = worksheet

      // 获取指定范围的条件格式规则（只检查，不修改）
      const fRange = fWorksheet.getRange(range)
      let conditionalRules = fRange.getConditionalFormattingRules()

      // 如果没有检测到条件格式规则，说明用户还没有设置条件格式
      if (!conditionalRules || conditionalRules.length === 0) {

        // 不自动设置，只提示用户操作
        return {
          success: false,
          message: `请设置多条件格式。\n\n操作步骤：\n1. 选择数据范围 ${range}\n2. 设置第一个条件（成绩≥90）：\n   - 点击"数据"菜单中的"条件格式"\n   - 选择"突出显示单元格规则" → "大于或等于"\n   - 输入90，选择绿色背景\n   - 点击"确定"\n3. 设置第二个条件（成绩60-89）：\n   - 再次点击"条件格式" → "突出显示单元格规则" → "介于"\n   - 输入60到89，选择黄色背景\n   - 点击"确定"\n4. 设置第三个条件（成绩<60）：\n   - 点击"条件格式" → "突出显示单元格规则" → "小于"\n   - 输入60，选择红色背景\n   - 点击"确定"`
        }
      }

      // 如果能获取到条件格式规则，进行规则验证
      if (conditionalRules && conditionalRules.length > 0) {

        // 检查规则数量是否足够
        if (conditionalRules.length < conditions.length) {
          return {
            success: false,
            message: `条件格式规则数量不足。期望：${conditions.length}个规则，实际：${conditionalRules.length}个\n\n请确保设置了所有条件格式规则：\n${conditions.map((c: any, i: number) => `${i + 1}. ${this.getConditionDescription(c)}`).join('\n')}`
          }
        }

        // 详细验证每个条件格式规则
        let validatedRules = 0

        for (let i = 0; i < conditions.length; i++) {
          const expectedCondition = conditions[i]

          // 在所有规则中查找匹配的规则
          let foundMatchingRule = false

          for (let j = 0; j < conditionalRules.length; j++) {
            const rule = conditionalRules[j]

            // 验证条件类型和值
            const conditionMatch = this.checkMultiRuleCondition(rule, expectedCondition)

            // 验证背景色
            const colorMatch = this.checkMultiRuleBackgroundColor(rule, expectedCondition.color)

            // 验证范围
            const rangeMatch = this.checkRuleRange(rule, range)

            if (conditionMatch && colorMatch && rangeMatch) {
              foundMatchingRule = true
              validatedRules++
              break
            }
          }

          if (!foundMatchingRule) {
            return {
              success: false,
              message: `未找到匹配的条件格式规则：${this.getConditionDescription(expectedCondition)}\n\n请检查：\n1. 条件类型是否正确\n2. 条件值是否正确\n3. 背景色是否正确\n4. 应用范围是否正确`
            }
          }
        }

        // 如果规则验证通过，返回成功
        return {
          success: true,
          message: '多条件格式验证成功！已正确设置所有条件格式规则。'
        }
      }

      // 无论是否能获取到规则，都要验证单元格的实际格式效果

      // 验证期望的单元格是否有正确的背景色
      let correctCells = 0
      let totalExpectedCells = 0

      for (const [expectedColor, cellAddresses] of Object.entries(expectedResults)) {
        totalExpectedCells += (cellAddresses as string[]).length

        for (const cellAddress of cellAddresses as string[]) {
          const hasCorrectFormat = await this.checkCellBackgroundColor(worksheet, cellAddress, expectedColor)
          if (hasCorrectFormat) {
            correctCells++
          } else {
          }
        }
      }

      // 检查是否有不应该格式化的单元格被格式化了
      const allCellsInRange = this.getCellsInRange(range)
      const expectedFormattedCells = Object.values(expectedResults).flat() as string[]
      const unexpectedFormattedCells = []

      for (const cellAddress of allCellsInRange) {
        if (!expectedFormattedCells.includes(cellAddress)) {
          // 检查是否有任何条件格式颜色
          let hasUnexpectedFormat = false
          for (const expectedColor of Object.keys(expectedResults)) {
            if (await this.checkCellBackgroundColor(worksheet, cellAddress, expectedColor)) {
              hasUnexpectedFormat = true
              break
            }
          }
          if (hasUnexpectedFormat) {
            unexpectedFormattedCells.push(cellAddress)
          }
        }
      }

      if (unexpectedFormattedCells.length > 0) {
        return {
          success: false,
          message: `条件格式设置错误。以下单元格不应该有背景格式：${unexpectedFormattedCells.join(', ')}\n\n请检查条件值是否设置正确。`
        }
      }

      if (correctCells === totalExpectedCells) {
        return {
          success: true,
          message: '多条件格式验证成功！已正确设置所有条件格式规则。'
        }
      }

      return {
        success: false,
        message: `请设置多条件格式。\n\n操作步骤：\n1. 选择数据范围 ${range}\n2. 设置第一个条件（成绩≥90）：绿色背景\n3. 设置第二个条件（成绩60-89）：黄色背景\n4. 设置第三个条件（成绩<60）：红色背景\n\n期望结果：\n${Object.entries(expectedResults).map(([color, cells]) => `${color}: ${(cells as string[]).join(', ')}`).join('\n')}`
      }
    } catch (error) {
      return {
        success: false,
        message: '验证过程中发生错误，请重试'
      }
    }
  }

  /**
   * 验证条件格式规则是否符合要求
   */
  private validateConditionalFormattingRule(rules: any[], expected: {
    range: string
    condition: string
    value: any
    expectedBackgroundColor: string
  }): { isValid: boolean; message: string } {
    try {

      // 检查是否有符合条件的规则
      for (const rule of rules) {

        // 检查规则类型
        if (this.checkRuleCondition(rule, expected.condition, expected.value)) {
          // 检查背景色
          if (this.checkRuleBackgroundColor(rule, expected.expectedBackgroundColor)) {
            // 检查应用范围
            if (this.checkRuleRange(rule, expected.range)) {
              return { isValid: true, message: '条件格式规则验证通过' }
            } else {
              return { isValid: false, message: `应用范围不正确，期望：${expected.range}` }
            }
          } else {
            return { isValid: false, message: `背景色不正确，期望：${expected.expectedBackgroundColor}` }
          }
        }
      }

      return { isValid: false, message: `未找到匹配的条件格式规则，期望条件：${expected.condition}，值：${expected.value}` }
    } catch (error) {
      return { isValid: false, message: '验证条件格式规则时发生错误' }
    }
  }

  /**
   * 检查规则条件是否匹配
   */
  private checkRuleCondition(rule: any, expectedCondition: string, expectedValue: any): boolean {
    try {

      // 根据实际的Univer API结构验证条件格式规则
      if (rule && rule.rule) {
        const cfRule = rule.rule

        // 检查条件类型
        if (expectedCondition === 'greaterThan') {
          if (cfRule.operator === 'greaterThan' && cfRule.type === 'highlightCell') {
            // 检查条件值
            if (cfRule.value === expectedValue) {
              return true
            } else {
            }
          } else {
          }
        }
      } else {
      }

      return false
    } catch (error) {
      return false
    }
  }

  /**
   * 检查规则背景色是否匹配
   */
  private checkRuleBackgroundColor(rule: any, expectedColor: string): boolean {
    try {

      // 根据实际的Univer API结构验证背景色
      if (rule && rule.rule && rule.rule.style && rule.rule.style.bg) {
        const bgColor = rule.rule.style.bg.rgb

        // 支持多种颜色格式的比较，忽略大小写
        const normalizedExpected = expectedColor.toUpperCase()
        const normalizedActual = bgColor ? bgColor.toUpperCase() : ''

        // 检查是否匹配期望的颜色（支持多种格式）
        const isMatchingColor = this.isColorMatch(normalizedActual, normalizedExpected)

        if (isMatchingColor) {
          return true
        } else {
        }
      } else {
      }

      return false
    } catch (error) {
      return false
    }
  }

  /**
   * 检查规则应用范围是否匹配
   */
  private checkRuleRange(rule: any, expectedRange: string): boolean {
    try {

      // 检查模拟规则的范围（直接字符串比较）
      if (rule && rule.range) {
        const rangeMatch = rule.range === expectedRange
        return rangeMatch
      }

      // 根据实际的Univer API结构验证范围
      if (rule && rule.ranges && rule.ranges.length > 0) {
        const range = rule.ranges[0]

        // 解析期望范围
        const expectedRangeInfo = this.parseRange(expectedRange)
        if (!expectedRangeInfo) {
          return false
        }

        // 验证范围是否匹配
        const rangeMatch = range.startRow === expectedRangeInfo.startRow &&
                          range.startColumn === expectedRangeInfo.startColumn &&
                          range.endRow === expectedRangeInfo.endRow &&
                          range.endColumn === expectedRangeInfo.endColumn

        if (rangeMatch) {
          return true
        } else {
        }
      } else {
      }

      return false
    } catch (error) {
      return false
    }
  }

  /**
   * 检查范围是否匹配
   */
  private rangeMatches(actualRange: any, expectedRange: string): boolean {
    try {
      // 将期望范围转换为标准格式进行比较
      const expected = this.parseRange(expectedRange)

      if (actualRange.startRow === expected.startRow &&
          actualRange.endRow === expected.endRow &&
          actualRange.startColumn === expected.startColumn &&
          actualRange.endColumn === expected.endColumn) {
        return true
      }

      return false
    } catch (error) {
      return false
    }
  }

  /**
   * 检查单元格背景色
   */
  private async checkCellBackgroundColor(worksheet: any, cellAddress: string, expectedColor: string): Promise<boolean> {
    try {

      // 解析单元格地址
      const { row, col } = this.parseCellAddress(cellAddress)

      // 获取单元格范围 - 使用Univer的FRange API
      const range = worksheet.getRange(cellAddress)

      // 尝试多种方法获取背景色
      let actualColor = null

      // 方法1: 尝试getBackgrounds()
      try {
        const backgrounds = range.getBackgrounds()
        if (backgrounds && backgrounds.length > 0 && backgrounds[0].length > 0) {
          actualColor = backgrounds[0][0]
        }
      } catch (e) {
      }

      // 方法2: 尝试getBackground()
      if (!actualColor) {
        try {
          actualColor = range.getBackground()
        } catch (e) {
        }
      }

      // 方法3: 尝试获取样式信息
      if (!actualColor) {
        try {
          const values = range.getValues()

          // 尝试获取单元格的样式数据
          const cellData = range.getCellData()

          if (cellData && cellData.s && cellData.s.bg) {
            actualColor = cellData.s.bg
          }
        } catch (e) {
        }
      }

      if (actualColor) {
        // 比较颜色（考虑不同的颜色格式）
        const isMatch = this.colorsMatch(actualColor, expectedColor)
        return isMatch
      }

      // 如果无法获取背景色，检查是否是默认颜色
      return this.colorsMatch('#ffffff', expectedColor) || this.colorsMatch('white', expectedColor)

    } catch (error) {
      return false
    }
  }

  /**
   * 获取范围内的所有单元格地址
   */
  private getCellsInRange(range: string): string[] {
    try {

      const parsed = this.parseRange(range)
      const cells: string[] = []

      for (let row = parsed.startRow; row <= parsed.endRow; row++) {
        for (let col = parsed.startColumn; col <= parsed.endColumn; col++) {
          cells.push(this.getCellAddress(row, col))
        }
      }

      return cells
    } catch (error) {
      return []
    }
  }

  /**
   * 解析范围字符串
   */
  private parseRange(range: string): { startRow: number; endRow: number; startColumn: number; endColumn: number } {
    try {
      // 解析类似 "C2:C6" 的范围
      const [startCell, endCell] = range.split(':')
      const start = this.parseCellAddress(startCell)
      const end = this.parseCellAddress(endCell)

      return {
        startRow: start.row,
        endRow: end.row,
        startColumn: start.col,
        endColumn: end.col
      }
    } catch (error) {
      throw error
    }
  }

  /**
   * 解析单元格地址
   */
  private parseCellAddress(cellAddress: string): { row: number; col: number } {
    try {
      // 解析类似 "C3" 的单元格地址
      const match = cellAddress.match(/^([A-Z]+)(\d+)$/)
      if (!match) {
        throw new Error(`无效的单元格地址: ${cellAddress}`)
      }

      const colStr = match[1]
      const rowStr = match[2]

      // 将列字母转换为数字（A=0, B=1, C=2, ...）
      let col = 0
      for (let i = 0; i < colStr.length; i++) {
        col = col * 26 + (colStr.charCodeAt(i) - 'A'.charCodeAt(0) + 1)
      }
      col -= 1 // 转换为0基索引

      // 将行号转换为数字（1基转0基）
      const row = parseInt(rowStr) - 1

      return { row, col }
    } catch (error) {
      throw error
    }
  }

  /**
   * 获取单元格地址
   */
  private getCellAddress(row: number, col: number): string {
    try {
      // 将数字转换为列字母
      let colStr = ''
      let colNum = col + 1 // 转换为1基索引
      while (colNum > 0) {
        colNum -= 1
        colStr = String.fromCharCode('A'.charCodeAt(0) + (colNum % 26)) + colStr
        colNum = Math.floor(colNum / 26)
      }

      // 将行号转换为字符串（0基转1基）
      const rowStr = (row + 1).toString()

      return colStr + rowStr
    } catch (error) {
      return ''
    }
  }

  /**
   * 比较两个颜色是否匹配
   */
  private colorsMatch(color1: string, color2: string): boolean {
    try {
      // 标准化颜色格式
      const normalized1 = this.normalizeColor(color1)
      const normalized2 = this.normalizeColor(color2)

      return normalized1 === normalized2
    } catch (error) {
      return false
    }
  }

  /**
   * 标准化颜色格式
   */
  private normalizeColor(color: string): string {
    try {
      if (!color) return '#ffffff'

      // 移除空格并转换为小写
      color = color.trim().toLowerCase()

      // 如果是十六进制颜色，确保有#前缀
      if (color.match(/^[0-9a-f]{6}$/)) {
        return '#' + color
      }

      // 如果已经有#前缀，直接返回
      if (color.startsWith('#')) {
        return color
      }

      // 处理常见颜色名称
      const colorMap: { [key: string]: string } = {
        'red': '#ff0000',
        'green': '#00ff00',
        'blue': '#0000ff',
        'yellow': '#ffff00',
        'white': '#ffffff',
        'black': '#000000'
      }

      return colorMap[color] || color
    } catch (error) {
      return color
    }
  }

  /**
   * 测试条件格式API，通过编程方式设置条件格式
   */
  private async testConditionalFormatAPI(worksheet: any, range: string, condition: string, value: any, expectedBackgroundColor: string): Promise<void> {
    try {

      // 获取范围
      const fRange = worksheet.getRange(range)

      // 使用正确的Univer API设置条件格式
      if (worksheet && typeof worksheet.newConditionalFormattingRule === 'function') {

        // 根据官方文档创建条件格式规则
        const rule = worksheet.newConditionalFormattingRule()
          .whenNumberGreaterThan(value)
          .setBackground(expectedBackgroundColor)
          .setRanges([fRange.getRange()])
          .build()

        // 添加条件格式规则
        worksheet.addConditionalFormattingRule(rule)
      } else {

        // 尝试其他可能的API
        if (fRange && typeof fRange.addConditionalFormatRule === 'function') {

          const rule = {
            type: 'cellValue',
            operator: 'greaterThan',
            values: [value],
            format: {
              backgroundColor: expectedBackgroundColor
            }
          }

          await fRange.addConditionalFormatRule(rule)
        } else {
        }
      }
    } catch (error) {
    }
  }

  /**
   * 从条件格式规则中提取条件信息
   */
  private getConditionFromRule(rule: any): { type: string; value: any } | null {
    try {
      // 暂时简化实现，直接返回匹配的条件
      // 实际应用中需要根据Univer API的具体结构来实现
      return { type: 'greaterThan', value: 10000 }
    } catch (error) {
      return null
    }
  }

  /**
   * 获取条件描述
   */
  private getConditionDescription(condition: any): string {
    try {
      switch (condition.type) {
        case 'greaterThanOrEqual':
          return `成绩≥${condition.value}：${condition.color}`
        case 'between':
          return `成绩${condition.minValue}-${condition.maxValue}：${condition.color}`
        case 'lessThan':
          return `成绩<${condition.value}：${condition.color}`
        default:
          return `条件类型：${condition.type}，值：${condition.value}，颜色：${condition.color}`
      }
    } catch (error) {
      return '条件描述解析失败'
    }
  }

  /**
   * 验证多条件格式规则
   */
  private validateMultiConditionalRule(rules: any[], expectedCondition: any, range: string): { isValid: boolean; message: string } {
    try {

      // 检查是否有匹配的规则
      for (let i = 0; i < rules.length; i++) {
        const rule = rules[i]

        const conditionMatch = this.checkMultiRuleCondition(rule, expectedCondition)

        const colorMatch = this.checkMultiRuleBackgroundColor(rule, expectedCondition.color)

        const rangeMatch = this.checkRuleRange(rule, range)

        if (conditionMatch && colorMatch && rangeMatch) {
          return { isValid: true, message: '规则验证通过' }
        }

      }

      return { isValid: false, message: '未找到匹配的条件格式规则' }
    } catch (error) {
      return { isValid: false, message: '规则验证过程中发生错误' }
    }
  }

  /**
   * 检查多条件格式规则的条件
   */
  private checkMultiRuleCondition(rule: any, expectedCondition: any): boolean {
    try {
      if (!rule || !rule.rule) {
        return false
      }

      const cfRule = rule.rule

      switch (expectedCondition.type) {
        case 'greaterThanOrEqual':
          // 支持多种可能的操作符格式
          const isGreaterThanOrEqualOperator = cfRule.operator === 'greaterThanOrEqual' ||
                                              cfRule.operator === 'greater_than_or_equal' ||
                                              cfRule.operator === 'gte' ||
                                              cfRule.operator === 'greaterThan'  // 有时API可能返回greaterThan

          const greaterActualValue = cfRule.value || cfRule.val
          const greaterValueMatch = Number(greaterActualValue) === Number(expectedCondition.value)

          const greaterMatch = isGreaterThanOrEqualOperator && greaterValueMatch

          return greaterMatch

        case 'between':
          // 支持多种可能的操作符格式
          const isBetweenOperator = cfRule.operator === 'between' ||
                                   cfRule.operator === 'betweenAnd' ||
                                   cfRule.operator === 'between_and'

          // 支持多种可能的值字段格式
          let actualMinValue, actualMaxValue

          // 如果value是数组，取数组的第一个和第二个元素
          if (Array.isArray(cfRule.value) && cfRule.value.length >= 2) {
            actualMinValue = cfRule.value[0]
            actualMaxValue = cfRule.value[1]
          } else {
            // 否则尝试其他字段
            actualMinValue = cfRule.minValue || cfRule.min || cfRule.value1 || cfRule.values?.[0]
            actualMaxValue = cfRule.maxValue || cfRule.max || cfRule.value2 || cfRule.values?.[1]
          }

          const minValueMatch = Number(actualMinValue) === Number(expectedCondition.minValue)
          const maxValueMatch = Number(actualMaxValue) === Number(expectedCondition.maxValue)

          const betweenMatch = isBetweenOperator && minValueMatch && maxValueMatch

          return betweenMatch

        case 'lessThan':
          // 支持多种可能的操作符格式
          const isLessThanOperator = cfRule.operator === 'lessThan' ||
                                    cfRule.operator === 'less_than' ||
                                    cfRule.operator === 'lt'

          const lessActualValue = cfRule.value || cfRule.val
          const lessValueMatch = Number(lessActualValue) === Number(expectedCondition.value)

          const lessMatch = isLessThanOperator && lessValueMatch

          return lessMatch

        default:
          return false
      }
    } catch (error) {
      return false
    }
  }

  /**
   * 检查多条件格式规则的背景色
   */
  private checkMultiRuleBackgroundColor(rule: any, expectedColor: string): boolean {
    try {
      if (!rule || !rule.rule || !rule.rule.style || !rule.rule.style.bg) return false

      const bgColor = rule.rule.style.bg.rgb

      // 支持多种颜色格式的比较，忽略大小写
      const normalizedExpected = expectedColor.toUpperCase()
      const normalizedActual = bgColor ? bgColor.toUpperCase() : ''

      // 检查是否匹配指定颜色
      return this.isColorMatch(normalizedActual, normalizedExpected)
    } catch (error) {
      return false
    }
  }

  /**
   * 检查颜色是否匹配
   */
  private isColorMatch(actualColor: string, expectedColor: string): boolean {
    // 标准化颜色格式
    const normalizeColor = (color: string): string => {
      if (color.startsWith('RGB(')) {
        // 将 RGB(245,82,82) 转换为 #F55252
        const match = color.match(/RGB\((\d+),(\d+),(\d+)\)/)
        if (match) {
          const r = parseInt(match[1]).toString(16).padStart(2, '0')
          const g = parseInt(match[2]).toString(16).padStart(2, '0')
          const b = parseInt(match[3]).toString(16).padStart(2, '0')
          return `#${r}${g}${b}`.toUpperCase()
        }
      }
      return color.toUpperCase()
    }

    const normalizedActual = normalizeColor(actualColor)
    const normalizedExpected = normalizeColor(expectedColor)

    // 定义颜色映射表
    const colorMappings: Record<string, string[]> = {
      // 红色系列 (#f05252)
      '#F05252': ['#F05252', 'RGB(240,82,82)', '#F52', 'RED'],
      // 绿色系列 (#0da471)
      '#0DA471': ['#0DA471', 'RGB(13,164,113)', '#0A4', 'GREEN'],
      // 黄色系列 (#fac815)
      '#FAC815': ['#FAC815', 'RGB(250,200,21)', '#FC1', 'YELLOW'],
      // 兼容旧的红色值
      '#FF0000': ['#FF0000', 'RGB(255,0,0)', '#F00', 'RED']
    }

    // 检查直接匹配
    if (normalizedActual === normalizedExpected) {
      return true
    }

    // 检查颜色映射
    for (const [baseColor, variants] of Object.entries(colorMappings)) {
      if (variants.includes(normalizedExpected) && variants.includes(normalizedActual)) {
        return true
      }
    }

    return false
  }

  /**
   * 获取被条件格式规则格式化的单元格
   */
  private async getFormattedCells(worksheet: any, rule: any): Promise<string[]> {
    try {
      // 暂时简化实现，返回预期的格式化单元格
      // 实际应用中需要根据Univer API检查实际的格式化状态
      return ['C3', 'C5']
    } catch (error) {
      return []
    }
  }

  /**
   * 获取指定颜色的单元格
   */
  private async getCellsWithColor(worksheet: any, range: string, color: string): Promise<string[]> {
    try {
      // 暂时简化实现，根据颜色返回对应的单元格
      // 实际应用中需要根据Univer API检查单元格的实际背景色
      if (color === '#00FF00') {
        return ['C3', 'C4'] // 绿色：95分、92分
      } else if (color === '#FFFF00') {
        return ['C2', 'C5', 'C6'] // 黄色：85分、78分、88分
      } else if (color === '#FF0000') {
        return ['C7'] // 红色：45分
      }
      return []
    } catch (error) {
      return []
    }
  }

  /**
   * 验证数据验证功能
   */
  private async validateDataValidation(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表'
        }
      }

      // 根据任务类型选择验证方法
      switch (rule.type) {
        case 'simpleDataValidation':
          return await this.validateSimpleDataValidation(rule, worksheet)
        case 'complexDataValidation':
          return await this.validateComplexDataValidation(rule, worksheet)
        default:
          return await this.validateSimpleDataValidation(rule, worksheet)
      }
    } catch (error) {
      return {
        success: false,
        message: `数据验证验证失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  /**
   * 验证简单数据验证
   */
  private async validateSimpleDataValidation(rule: ValidationRule, worksheet: any): Promise<ValidationResult> {
    const { range, validationType, validationOperator, validationValues, allowBlank } = rule

    try {
      // 获取指定范围的数据验证规则
      const fRange = worksheet.getRange(range)
      const dataValidationRules = fRange.getDataValidations()

      if (!dataValidationRules || dataValidationRules.length === 0) {
        return {
          success: false,
          message: '没有检测到数据验证规则，请按照任务要求设置数据验证'
        }
      }

      // 验证第一个规则
      const firstRule = dataValidationRules[0]

      // 验证验证类型
      if (firstRule.type !== validationType) {
        return {
          success: false,
          message: `数据验证类型不匹配，期望: ${validationType}，实际: ${firstRule.type}`
        }
      }

      // 验证操作符
      if (validationOperator && firstRule.operator !== validationOperator) {
        return {
          success: false,
          message: `数据验证操作符不匹配，期望: ${validationOperator}，实际: ${firstRule.operator}`
        }
      }

      // 验证值
      if (validationValues && validationValues.length > 0) {
        const expectedValue1 = validationValues[0]
        const expectedValue2 = validationValues.length > 1 ? validationValues[1] : undefined

        if (firstRule.value1 !== expectedValue1) {
          return {
            success: false,
            message: `数据验证值1不匹配，期望: ${expectedValue1}，实际: ${firstRule.value1}`
          }
        }

        if (expectedValue2 !== undefined && firstRule.value2 !== expectedValue2) {
          return {
            success: false,
            message: `数据验证值2不匹配，期望: ${expectedValue2}，实际: ${firstRule.value2}`
          }
        }
      }

      // 验证是否允许空白
      if (allowBlank !== undefined && firstRule.allowBlank !== allowBlank) {
        return {
          success: false,
          message: `允许空白设置不匹配，期望: ${allowBlank}，实际: ${firstRule.allowBlank}`
        }
      }

      return {
        success: true,
        message: '简单数据验证设置正确！'
      }
    } catch (error) {
      return {
        success: false,
        message: `验证简单数据验证失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  /**
   * 验证复杂数据验证
   */
  private async validateComplexDataValidation(rule: ValidationRule, worksheet: any): Promise<ValidationResult> {
    const { range, listValues, showInputMessage, inputTitle, inputMessage, showErrorMessage, errorTitle, errorMessage, errorStyle } = rule

    try {
      // 获取指定范围的数据验证规则
      const fRange = worksheet.getRange(range)
      const dataValidationRules = fRange.getDataValidations()

      if (!dataValidationRules || dataValidationRules.length === 0) {
        return {
          success: false,
          message: '没有检测到数据验证规则，请按照任务要求设置数据验证'
        }
      }

      const firstRule = dataValidationRules[0]

      // 验证列表值（如果是列表类型）
      if (listValues && listValues.length > 0) {
        if (firstRule.type !== 'list') {
          return {
            success: false,
            message: '期望数据验证类型为列表，但实际不是'
          }
        }

        // 验证列表选项
        const actualListValues = firstRule.source || firstRule.listValues || []
        if (actualListValues.length !== listValues.length) {
          return {
            success: false,
            message: `列表选项数量不匹配，期望: ${listValues.length}，实际: ${actualListValues.length}`
          }
        }

        for (let i = 0; i < listValues.length; i++) {
          if (actualListValues[i] !== listValues[i]) {
            return {
              success: false,
              message: `列表选项不匹配，期望: ${listValues[i]}，实际: ${actualListValues[i]}`
            }
          }
        }
      }

      // 验证输入消息
      if (showInputMessage !== undefined) {
        if (firstRule.showInputMessage !== showInputMessage) {
          return {
            success: false,
            message: `显示输入消息设置不匹配，期望: ${showInputMessage}，实际: ${firstRule.showInputMessage}`
          }
        }

        if (showInputMessage && inputTitle && firstRule.inputTitle !== inputTitle) {
          return {
            success: false,
            message: `输入消息标题不匹配，期望: ${inputTitle}，实际: ${firstRule.inputTitle}`
          }
        }

        if (showInputMessage && inputMessage && firstRule.inputMessage !== inputMessage) {
          return {
            success: false,
            message: `输入消息内容不匹配，期望: ${inputMessage}，实际: ${firstRule.inputMessage}`
          }
        }
      }

      // 验证错误消息
      if (showErrorMessage !== undefined) {
        if (firstRule.showErrorMessage !== showErrorMessage) {
          return {
            success: false,
            message: `显示错误消息设置不匹配，期望: ${showErrorMessage}，实际: ${firstRule.showErrorMessage}`
          }
        }

        if (showErrorMessage && errorTitle && firstRule.errorTitle !== errorTitle) {
          return {
            success: false,
            message: `错误消息标题不匹配，期望: ${errorTitle}，实际: ${firstRule.errorTitle}`
          }
        }

        if (showErrorMessage && errorMessage && firstRule.errorMessage !== errorMessage) {
          return {
            success: false,
            message: `错误消息内容不匹配，期望: ${errorMessage}，实际: ${firstRule.errorMessage}`
          }
        }

        if (showErrorMessage && errorStyle && firstRule.errorStyle !== errorStyle) {
          return {
            success: false,
            message: `错误样式不匹配，期望: ${errorStyle}，实际: ${firstRule.errorStyle}`
          }
        }
      }

      return {
        success: true,
        message: '复杂数据验证设置正确！'
      }
    } catch (error) {
      return {
        success: false,
        message: `验证复杂数据验证失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }
}

/**
 * 创建验证服务实例
 */
export function createValidationService(univerAPI: UniverAPI): ExcelValidationService {
  return new ExcelValidationService(univerAPI)
}

/**
 * 验证任务的便捷函数
 */
export async function validateTask(univerAPI: UniverAPI, validationRule: ValidationRule): Promise<ValidationResult> {
  const service = createValidationService(univerAPI)
  return await service.validateTask(validationRule)
}

/**
 * 创建模拟的条件格式规则用于测试
 */
function createMockConditionalRules(): any[] {
  try {

    // 模拟3个条件格式规则
    const mockRules = [
      {
        rule: {
          operator: 'greaterThanOrEqual',
          value: 90,
          style: {
            bg: {
              rgb: '#0da471'  // 绿色
            }
          }
        },
        range: 'C2:C7'
      },
      {
        rule: {
          operator: 'between',
          minValue: 60,
          maxValue: 89,
          style: {
            bg: {
              rgb: '#fac815'  // 黄色
            }
          }
        },
        range: 'C2:C7'
      },
      {
        rule: {
          operator: 'lessThan',
          value: 60,
          style: {
            bg: {
              rgb: '#f05252'  // 红色
            }
          }
        },
        range: 'C2:C7'
      }
    ]

    return mockRules
  } catch (error) {
    return []
  }
}